document.addEventListener('DOMContentLoaded', () => {
    const carousel = document.querySelector('.carousel');
    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.carousel-prev');
    const nextBtn = document.querySelector('.carousel-next');
    let currentSlide = 0;
    let touchStartX = 0;
    let touchEndX = 0;
    let isSwiping = false;

    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        slides[index].classList.add('active');
        dots[index].classList.add('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(currentSlide);
    }

    // Event listeners for carousel controls
    nextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        nextSlide();
        resetAutoAdvance();
    });

    prevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        prevSlide();
        resetAutoAdvance();
    });

    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentSlide = index;
            showSlide(currentSlide);
            resetAutoAdvance();
        });
    });

    // Touch events for mobile swipe
    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        isSwiping = true;
        clearInterval(autoAdvanceTimer);
    }, { passive: true });

    carousel.addEventListener('touchend', (e) => {
        if (!isSwiping) return;

        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
        isSwiping = false;
        resetAutoAdvance();
    }, { passive: true });

    function handleSwipe() {
        const swipeThreshold = 50;

        // Swipe left (next slide)
        if (touchStartX - touchEndX > swipeThreshold) {
            nextSlide();
        }

        // Swipe right (previous slide)
        if (touchEndX - touchStartX > swipeThreshold) {
            prevSlide();
        }
    }

    // Auto-advance carousel with smooth transitions
    const autoAdvanceInterval = 5000; // 5 seconds between slides
    let autoAdvanceTimer = setInterval(nextSlide, autoAdvanceInterval);

    function resetAutoAdvance() {
        clearInterval(autoAdvanceTimer);
        autoAdvanceTimer = setInterval(nextSlide, autoAdvanceInterval);
    }

    // Pause auto-advance on hover (desktop only)
    carousel.addEventListener('mouseenter', () => {
        clearInterval(autoAdvanceTimer);
    });

    carousel.addEventListener('mouseleave', () => {
        resetAutoAdvance();
    });

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            clearInterval(autoAdvanceTimer);
        } else {
            resetAutoAdvance();
        }
    });
});