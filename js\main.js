/**
 * EDEN Pharmaceuticals - Main JavaScript Entry Point
 * This file imports and initializes all modules
 */

import navigation from './modules/navigation.js';
import carousel from './modules/carousel.js';
import animations from './modules/animations.js';
import forms from './modules/forms.js';
import { lazyLoadImages, addPreloadHints } from './modules/utils.js';

// Initialize modules when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize navigation features
    navigation.init();

    // Initialize carousel with options
    carousel.initCarousel({
        autoAdvanceInterval: 5000,
        pauseOnHover: true
    });

    // Initialize animations
    animations.init();

    // Initialize forms
    forms.init();

    // Initialize lazy loading for images
    lazyLoadImages('img[data-src]');

    // Add preload hints for critical resources
    addPreloadHints([
        { rel: 'preconnect', url: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', url: 'https://cdnjs.cloudflare.com' }
    ]);

    // Load page-specific modules if needed
    loadPageSpecificModules();
});

/**
 * Load page-specific JavaScript modules based on current page
 */
function loadPageSpecificModules() {
    // Check if we're on the about page
    if (document.querySelector('.about-hero') ||
        document.querySelector('.timeline-section') ||
        document.querySelector('.team-section')) {

        import('./modules/about.js')
            .then(module => {
                module.default.init();
            })
            .catch(error => {
                console.error('Error loading about page module:', error);
            });
    }

    // Add other page-specific module loading as needed
}
