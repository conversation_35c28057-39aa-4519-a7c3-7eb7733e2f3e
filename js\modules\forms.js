/**
 * Forms Module
 * Handles all form-related functionality
 */

/**
 * Initialize form validation
 * @param {string} formSelector - CSS selector for the form
 */
export function initFormValidation(formSelector = '.contact-form') {
    const form = document.querySelector(formSelector);
    if (!form) return;
    
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        // Basic validation
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('error');
                
                // Add error message if it doesn't exist
                let errorMsg = field.nextElementSibling;
                if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                    errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');
                    errorMsg.textContent = 'This field is required';
                    field.parentNode.insertBefore(errorMsg, field.nextSibling);
                }
            } else {
                field.classList.remove('error');
                
                // Remove error message if it exists
                const errorMsg = field.nextElementSibling;
                if (errorMsg && errorMsg.classList.contains('error-message')) {
                    errorMsg.remove();
                }
            }
        });
        
        // Email validation
        const emailField = form.querySelector('input[type="email"]');
        if (emailField && emailField.value.trim()) {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(emailField.value)) {
                isValid = false;
                emailField.classList.add('error');
                
                // Add error message if it doesn't exist
                let errorMsg = emailField.nextElementSibling;
                if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                    errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');
                    errorMsg.textContent = 'Please enter a valid email address';
                    emailField.parentNode.insertBefore(errorMsg, emailField.nextSibling);
                }
            }
        }
        
        // If form is valid, submit it
        if (isValid) {
            // Here you would normally send the form data to a server
            // For now, we'll just show a success message
            const successMsg = document.createElement('div');
            successMsg.classList.add('success-message');
            successMsg.textContent = 'Thank you for your message! We will get back to you soon.';
            
            // Remove any existing success message
            const existingSuccessMsg = form.querySelector('.success-message');
            if (existingSuccessMsg) {
                existingSuccessMsg.remove();
            }
            
            form.appendChild(successMsg);
            form.reset();
            
            // Remove success message after 5 seconds
            setTimeout(() => {
                successMsg.remove();
            }, 5000);
        }
    });
    
    // Remove error styling when user starts typing
    form.querySelectorAll('input, textarea').forEach(field => {
        field.addEventListener('input', () => {
            field.classList.remove('error');
            
            // Remove error message if it exists
            const errorMsg = field.nextElementSibling;
            if (errorMsg && errorMsg.classList.contains('error-message')) {
                errorMsg.remove();
            }
        });
    });
}

/**
 * Initialize all form features
 */
export function init() {
    initFormValidation();
}

export default { init };
