<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test Page</title>
    <link rel="stylesheet" href="css/optimized.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Add console logging to check for errors -->
    <script>
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.message, 'at', e.filename, 'line', e.lineno);
            document.getElementById('error-log').innerHTML += '<p>Error: ' + e.message + ' at ' + e.filename + ' line ' + e.lineno + '</p>';
        });
        
        console.log('Page loading...');
    </script>
</head>
<body>
    <div class="wrapper">
        <header>
            <nav class="main-nav">
                <button class="mobile-menu-btn"><i class="fas fa-bars"></i></button>
                <div class="logo">JS Test</div>
                <ul class="nav-links">
                    <li class="active"><a href="#">Test</a></li>
                </ul>
            </nav>
        </header>
        <main style="margin-top: 100px; padding: 20px;">
            <div class="container">
                <h1>JavaScript Test Page</h1>
                <p>This page tests if the JavaScript is loading correctly.</p>
                
                <div id="error-log" style="margin-top: 20px; padding: 10px; background-color: #ffeeee; border: 1px solid #ffcccc;">
                    <h3>JavaScript Error Log:</h3>
                </div>
                
                <div style="margin-top: 20px;">
                    <button id="test-btn" class="btn-primary">Test JavaScript</button>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Load JavaScript files one by one to check which one causes issues -->
    <script src="js/optimized.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded');
            document.getElementById('error-log').innerHTML += '<p>DOM fully loaded</p>';
            
            // Test button functionality
            document.getElementById('test-btn').addEventListener('click', function() {
                console.log('Button clicked');
                document.getElementById('error-log').innerHTML += '<p>Button clicked successfully</p>';
            });
        });
    </script>
</body>
</html>
