/**
 * Animations Module
 * Handles all animation-related functionality
 */

/**
 * Throttle function to limit execution frequency
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in ms
 * @returns {Function} Throttled function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Check if an element is in viewport
 * @param {HTMLElement} element - Element to check
 * @param {number} offset - Offset from viewport edge
 * @returns {boolean} Whether element is in viewport
 */
function isInViewport(element, offset = 0) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top <= (window.innerHeight - offset) &&
        rect.bottom >= 0
    );
}

/**
 * Initialize stats counter animation
 */
export function initStatsCounter() {
    const stats = document.querySelectorAll('.stat-number');
    if (!stats.length) return;
    
    let animated = false;

    function animateStats() {
        if (animated) return;

        stats.forEach(stat => {
            const target = parseInt(stat.getAttribute('data-count'));
            let current = 0;
            const increment = target / 50; // Adjust speed here
            const duration = 1500; // Animation duration in ms
            const stepTime = duration / (target / increment);
            
            const counter = setInterval(() => {
                current += increment;
                stat.textContent = Math.floor(current);
                if (current >= target) {
                    stat.textContent = target;
                    clearInterval(counter);
                }
            }, stepTime);
        });

        animated = true;
    }

    // Use Intersection Observer instead of scroll event
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStats();
                observer.disconnect(); // Stop observing once animation is triggered
            }
        });
    }, { threshold: 0.1 });

    // Observe the first stat element
    if (stats.length > 0) {
        const statsContainer = stats[0].closest('.intro-stats') || stats[0].parentElement;
        if (statsContainer) {
            observer.observe(statsContainer);
        }
    }
}

/**
 * Initialize scroll animations for elements
 * @param {Object} options - Configuration options
 * @param {string} options.selector - CSS selector for elements to animate
 * @param {string} options.activeClass - Class to add when element is in viewport
 * @param {number} options.threshold - Viewport threshold for triggering animation
 * @param {number} options.delay - Base delay for staggered animations
 */
export function initScrollAnimations(options = {}) {
    const defaultOptions = {
        selector: '.animate-on-scroll',
        activeClass: 'animated',
        threshold: 0.1,
        delay: 100
    };
    
    const settings = { ...defaultOptions, ...options };
    const elements = document.querySelectorAll(settings.selector);
    
    if (!elements.length) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add staggered delay based on index
                setTimeout(() => {
                    entry.target.classList.add(settings.activeClass);
                }, settings.delay * index);
                
                // Stop observing this element
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: settings.threshold });
    
    // Observe all elements
    elements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * Initialize parallax effect for background images
 */
export function initParallax() {
    const parallaxElements = document.querySelectorAll('.parallax-image');
    if (!parallaxElements.length) return;
    
    // Don't use parallax on mobile devices
    if (window.innerWidth <= 768) {
        parallaxElements.forEach(element => {
            element.style.backgroundAttachment = 'scroll';
        });
        return;
    }
    
    const handleScroll = throttle(() => {
        parallaxElements.forEach(element => {
            if (isInViewport(element)) {
                const scrollPosition = window.pageYOffset;
                const speed = element.getAttribute('data-speed') || 0.5;
                element.style.backgroundPositionY = `${scrollPosition * speed}px`;
            }
        });
    }, 10);
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Initial call
    handleScroll();
}

/**
 * Initialize all animation features
 */
export function init() {
    initStatsCounter();
    initScrollAnimations();
    initParallax();
    
    // Add specific animations for different sections
    initScrollAnimations({
        selector: '.feature',
        activeClass: 'animated',
        threshold: 0.2,
        delay: 150
    });
    
    initScrollAnimations({
        selector: '.product-item',
        activeClass: 'animated',
        threshold: 0.1,
        delay: 100
    });
}

export default { init };
