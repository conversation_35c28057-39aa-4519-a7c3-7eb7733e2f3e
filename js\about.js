/**
 * EDKEM Pharmaceuticals - About Page JavaScript
 * This file contains interactive functionality specific to the About page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add console log for debugging
    console.log('About page JS loaded');

    try {
        // ===== Interactive Timeline =====
        initializeInteractiveTimeline();

        // ===== Interactive Core Values =====
        initializeFlipCards();

        // ===== Team Member Modals =====
        initializeTeamModals();

        // ===== Enhanced Achievement Counters =====
        initializeEnhancedCounters();

        // ===== FAQ Accordion =====
        initializeFaqAccordion();

        // ===== Responsive Parallax Effect =====
        initializeResponsiveParallax();

        console.log('All about page functions initialized successfully');
    } catch (error) {
        console.error('Error initializing about page functions:', error);
    }
});

/**
 * Initialize the simplified timeline
 */
function initializeInteractiveTimeline() {
    const timelineItems = document.querySelectorAll('.timeline-item');

    if (timelineItems.length > 0) {
        // Make timeline items visible without interactive elements
        timelineItems.forEach(item => {
            // Remove interactive class if it exists
            item.classList.remove('interactive');
            // Add visible class immediately
            item.classList.add('visible');

            // Ensure the year is properly displayed
            const yearElement = item.querySelector('.timeline-year');
            if (yearElement) {
                // Make sure the year is visible and properly styled
                yearElement.style.display = 'flex';
                yearElement.style.alignItems = 'center';
                yearElement.style.justifyContent = 'center';
            }
        });
    }
}

/**
 * Initialize flip card animations for core values
 */
function initializeFlipCards() {
    const valueItems = document.querySelectorAll('.value-item');

    if (valueItems.length > 0) {
        valueItems.forEach(item => {
            // Add flip card classes
            item.classList.add('flip-card');

            // Get the content
            const icon = item.querySelector('.value-icon').outerHTML;
            const title = item.querySelector('h3').outerHTML;
            const description = item.querySelector('p').outerHTML;

            // Create the flip card structure
            const flipCardInner = document.createElement('div');
            flipCardInner.className = 'flip-card-inner';

            // Create front side
            const flipCardFront = document.createElement('div');
            flipCardFront.className = 'flip-card-front';
            flipCardFront.innerHTML = `
                ${icon}
                ${title}
            `;

            // Create back side with more detailed content
            const flipCardBack = document.createElement('div');
            flipCardBack.className = 'flip-card-back';
            flipCardBack.innerHTML = `
                <h3>${item.querySelector('h3').textContent}</h3>
                ${description}
                <div class="flip-card-action">
                    <button class="flip-back-btn">Back</button>
                </div>
            `;

            // Assemble the flip card
            flipCardInner.appendChild(flipCardFront);
            flipCardInner.appendChild(flipCardBack);

            // Clear the original content and add the flip card
            item.innerHTML = '';
            item.appendChild(flipCardInner);

            // Add click event to flip the card
            item.addEventListener('click', function(e) {
                // Don't flip back if the back button was clicked
                if (e.target.classList.contains('flip-back-btn')) {
                    this.classList.remove('flipped');
                    e.stopPropagation();
                } else {
                    this.classList.toggle('flipped');
                }
            });
        });
    }
}

/**
 * Initialize team member modal popups
 */
function initializeTeamModals() {
    const teamMembers = document.querySelectorAll('.team-member');

    if (teamMembers.length > 0) {
        // Create modal container if it doesn't exist
        let modalContainer = document.querySelector('.team-modal-container');
        if (!modalContainer) {
            modalContainer = document.createElement('div');
            modalContainer.className = 'team-modal-container';
            document.body.appendChild(modalContainer);
        }

        // Add click event to each team member
        teamMembers.forEach(member => {
            member.addEventListener('click', function() {
                const name = this.querySelector('h3').textContent;
                const position = this.querySelector('.member-position').textContent;
                const bio = this.querySelector('.member-bio').textContent;
                const imgSrc = this.querySelector('img').src;

                // Create modal content
                const modalContent = `
                    <div class="team-modal">
                        <div class="team-modal-content">
                            <span class="close-modal">&times;</span>
                            <div class="modal-header">
                                <img src="${imgSrc}" alt="${name}">
                                <div class="modal-header-info">
                                    <h2>${name}</h2>
                                    <p class="modal-position">${position}</p>
                                    <div class="modal-social">
                                        <a href="#" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                        <a href="#" target="_blank"><i class="fab fa-twitter"></i></a>
                                        <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i></a>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-body">
                                <h3>Biography</h3>
                                <p>${bio}</p>
                                <h3>Expertise</h3>
                                <ul class="expertise-list">
                                    <li><i class="fas fa-check-circle"></i> Pharmaceutical Research</li>
                                    <li><i class="fas fa-check-circle"></i> Strategic Leadership</li>
                                    <li><i class="fas fa-check-circle"></i> Product Development</li>
                                    <li><i class="fas fa-check-circle"></i> Industry Innovation</li>
                                </ul>
                                <h3>Contact</h3>
                                <p>For inquiries, please email <a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>
                    </div>
                `;

                // Add modal to container
                modalContainer.innerHTML = modalContent;

                // Show modal container and modal
                modalContainer.classList.add('active');
                setTimeout(() => {
                    modalContainer.querySelector('.team-modal').classList.add('show');
                }, 10);

                // Add close event
                const closeBtn = modalContainer.querySelector('.close-modal');
                closeBtn.addEventListener('click', function() {
                    modalContainer.querySelector('.team-modal').classList.remove('show');
                    setTimeout(() => {
                        modalContainer.classList.remove('active');
                        modalContainer.innerHTML = '';
                    }, 300);
                });

                // Close when clicking outside the modal
                modalContainer.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.querySelector('.team-modal').classList.remove('show');
                        setTimeout(() => {
                            this.classList.remove('active');
                            this.innerHTML = '';
                        }, 300);
                    }
                });
            });

            // Add a class to indicate it's clickable
            member.classList.add('clickable');
        });
    }
}

/**
 * Initialize achievement counters
 */
function initializeEnhancedCounters() {
    const achievementNumbers = document.querySelectorAll('.achievement-number');

    if (achievementNumbers.length > 0) {
        // Ensure numbers are visible and properly styled
        achievementNumbers.forEach(number => {
            number.style.display = 'flex';
            number.style.alignItems = 'center';
            number.style.justifyContent = 'center';
            number.style.textAlign = 'center';
            number.style.lineHeight = '1';
        });

        console.log('Achievement counters initialized with enhanced display');
    } else {
        console.log('No achievement counters found');
    }
}



/**
 * Initialize FAQ accordion functionality
 */
function initializeFaqAccordion() {
    const faqQuestions = document.querySelectorAll('.faq-question');

    if (faqQuestions.length > 0) {
        faqQuestions.forEach(question => {
            question.addEventListener('click', function() {
                const faqItem = this.parentElement;
                const isActive = faqItem.classList.contains('active');

                // Close all FAQ items
                document.querySelectorAll('.faq-item').forEach(item => {
                    item.classList.remove('active');
                    const toggle = item.querySelector('.faq-toggle i');
                    if (toggle) {
                        toggle.className = 'fas fa-plus';
                    }
                });

                // Open the clicked item if it wasn't already active
                if (!isActive) {
                    faqItem.classList.add('active');
                    const toggle = this.querySelector('.faq-toggle i');
                    if (toggle) {
                        toggle.className = 'fas fa-minus';
                    }
                }
            });
        });

        // Add animation to FAQ items when they come into view
        const faqObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animated');
                    }, index * 100);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.faq-item').forEach(item => {
            faqObserver.observe(item);
        });
    }
}

/**
 * Initialize responsive parallax effect for the about image section
 * This function handles the parallax effect differently based on screen size
 */
function initializeResponsiveParallax() {
    const parallaxImage = document.querySelector('.parallax-image');

    if (parallaxImage) {
        // Check if device is mobile
        const isMobile = window.innerWidth <= 768;

        // Apply appropriate background attachment based on device
        if (isMobile) {
            parallaxImage.style.backgroundAttachment = 'scroll';
        } else {
            parallaxImage.style.backgroundAttachment = 'fixed';
        }

        // Add resize listener to adjust on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768) {
                parallaxImage.style.backgroundAttachment = 'scroll';
            } else {
                parallaxImage.style.backgroundAttachment = 'fixed';
            }
        });

        // Add scroll animation for content
        const imageContent = document.querySelector('.image-content');
        if (imageContent) {
            // Create intersection observer to animate content when in view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.querySelector('.content-animation').style.opacity = '1';
                        entry.target.querySelector('.content-animation').style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.2 });

            observer.observe(imageContent);
        }

        console.log('Responsive parallax initialized');
    } else {
        console.log('No parallax image found');
    }
}