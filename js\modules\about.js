/**
 * About Page Module
 * Handles functionality specific to the About page
 */

import { throttle } from './utils.js';

/**
 * Initialize interactive timeline
 */
export function initializeInteractiveTimeline() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    if (!timelineItems.length) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('active');
                }, 150 * index);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.2 });
    
    timelineItems.forEach(item => {
        observer.observe(item);
    });
}

/**
 * Initialize flip cards for core values
 */
export function initializeFlipCards() {
    const flipCards = document.querySelectorAll('.flip-card');
    if (!flipCards.length) return;
    
    flipCards.forEach(card => {
        card.addEventListener('click', () => {
            card.classList.toggle('flipped');
        });
        
        // Also flip on keyboard enter/space for accessibility
        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                card.classList.toggle('flipped');
            }
        });
    });
}

/**
 * Initialize team member modals
 */
export function initializeTeamModals() {
    const teamMembers = document.querySelectorAll('.team-member');
    if (!teamMembers.length) return;
    
    const body = document.body;
    let activeModal = null;
    
    teamMembers.forEach(member => {
        member.addEventListener('click', () => {
            const name = member.querySelector('h3').textContent;
            const position = member.querySelector('.position').textContent;
            const imgSrc = member.querySelector('img').src;
            const bio = member.getAttribute('data-bio') || 'Biography information not available.';
            
            // Create modal content
            const modalContent = `
                <div class="team-modal">
                    <div class="team-modal-content">
                        <span class="close-modal">&times;</span>
                        <div class="modal-header">
                            <img src="${imgSrc}" alt="${name}">
                            <div class="modal-header-info">
                                <h2>${name}</h2>
                                <p class="modal-position">${position}</p>
                                <div class="modal-social">
                                    <a href="#" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                    <a href="#" target="_blank"><i class="fab fa-twitter"></i></a>
                                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="modal-body">
                            <h3>Biography</h3>
                            <p>${bio}</p>
                            <h3>Expertise</h3>
                            <ul class="expertise-list">
                                <li><i class="fas fa-check-circle"></i> Pharmaceutical Research</li>
                                <li><i class="fas fa-check-circle"></i> Product Development</li>
                                <li><i class="fas fa-check-circle"></i> Quality Assurance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to DOM
            const modalElement = document.createElement('div');
            modalElement.innerHTML = modalContent;
            body.appendChild(modalElement.firstElementChild);
            
            // Get the newly added modal
            activeModal = document.querySelector('.team-modal');
            
            // Prevent body scrolling
            body.style.overflow = 'hidden';
            
            // Add close functionality
            const closeBtn = activeModal.querySelector('.close-modal');
            closeBtn.addEventListener('click', closeModal);
            
            // Close when clicking outside the modal content
            activeModal.addEventListener('click', (e) => {
                if (e.target === activeModal) {
                    closeModal();
                }
            });
            
            // Close on escape key
            document.addEventListener('keydown', handleEscKey);
        });
    });
    
    function closeModal() {
        if (activeModal) {
            activeModal.remove();
            activeModal = null;
            body.style.overflow = '';
            document.removeEventListener('keydown', handleEscKey);
        }
    }
    
    function handleEscKey(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    }
}

/**
 * Initialize enhanced achievement counters
 */
export function initializeEnhancedCounters() {
    const counters = document.querySelectorAll('.achievement-counter');
    if (!counters.length) return;
    
    let animated = false;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !animated) {
                animated = true;
                
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-count'));
                    const duration = 2000; // ms
                    const stepTime = Math.abs(Math.floor(duration / target));
                    let current = 0;
                    
                    const counterInterval = setInterval(() => {
                        current += 1;
                        counter.textContent = current;
                        
                        if (current >= target) {
                            counter.textContent = target;
                            clearInterval(counterInterval);
                        }
                    }, stepTime);
                });
                
                observer.disconnect();
            }
        });
    }, { threshold: 0.1 });
    
    // Observe the container of counters
    const counterSection = counters[0].closest('.achievements-section') || counters[0].parentElement;
    if (counterSection) {
        observer.observe(counterSection);
    }
}

/**
 * Initialize FAQ accordion
 */
export function initializeFaqAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');
    if (!faqItems.length) return;
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            const isOpen = item.classList.contains('active');
            
            // Close all items
            faqItems.forEach(faq => {
                faq.classList.remove('active');
            });
            
            // Open clicked item if it wasn't already open
            if (!isOpen) {
                item.classList.add('active');
            }
        });
    });
}

/**
 * Initialize responsive parallax effect
 */
export function initializeResponsiveParallax() {
    const parallaxImage = document.querySelector('.parallax-image');
    if (!parallaxImage) return;
    
    // Disable parallax on mobile for better performance
    if (window.innerWidth <= 768) {
        parallaxImage.style.backgroundAttachment = 'scroll';
        return;
    }
    
    const handleScroll = throttle(() => {
        const scrollPosition = window.pageYOffset;
        const speed = 0.5; // Adjust for faster/slower parallax
        
        parallaxImage.style.backgroundPositionY = `calc(50% + ${scrollPosition * speed}px)`;
    }, 10);
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Initial call
    handleScroll();
}

/**
 * Initialize all about page features
 */
export function init() {
    try {
        initializeInteractiveTimeline();
        initializeFlipCards();
        initializeTeamModals();
        initializeEnhancedCounters();
        initializeFaqAccordion();
        initializeResponsiveParallax();
    } catch (error) {
        console.error('Error initializing about page functions:', error);
    }
}

export default { init };
