/**
 * EDKEM Pharmaceuticals - Main JavaScript File
 * This file contains all the JavaScript functionality for the website
 */

document.addEventListener('DOMContentLoaded', function() {
    // ===== Mobile Menu Toggle =====
    const nav = document.querySelector('.main-nav');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const menuItems = navLinks ? navLinks.querySelectorAll('a') : [];

    // Toggle mobile menu
    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            navLinks.classList.toggle('show');
            mobileMenuBtn.innerHTML = navLinks.classList.contains('show')
                ? '<i class="fas fa-times"></i>'
                : '<i class="fas fa-bars"></i>';
            document.body.style.overflow = navLinks.classList.contains('show') ? 'hidden' : '';
        });
    }

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (nav && navLinks && !nav.contains(e.target) && navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.style.overflow = '';
        }
    });

    // Close menu when clicking on menu items
    if (menuItems.length > 0) {
        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                if (window.innerWidth <= 768 && navLinks.classList.contains('show')) {
                    navLinks.classList.remove('show');
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                    document.body.style.overflow = '';
                }
            });
        });
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        if (navLinks && window.innerWidth > 768 && navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
            if (mobileMenuBtn) {
                mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            }
            document.body.style.overflow = '';
        }
    });

    // Add touch support for mobile devices
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
    }, false);

    document.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, false);

    function handleSwipe() {
        // Swipe right to open menu
        if (navLinks && touchEndX - touchStartX > 100 && !navLinks.classList.contains('show')) {
            navLinks.classList.add('show');
            if (mobileMenuBtn) {
                mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
            }
            document.body.style.overflow = 'hidden';
        }

        // Swipe left to close menu
        if (navLinks && touchStartX - touchEndX > 100 && navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
            if (mobileMenuBtn) {
                mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            }
            document.body.style.overflow = '';
        }
    }

    // ===== Testimonial Slider =====
    const testimonialDots = document.querySelectorAll('.testimonial-dot');
    const testimonialItems = document.querySelectorAll('.testimonial-item');

    if (testimonialDots.length > 0 && testimonialItems.length > 0) {
        testimonialDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                // Remove active class from all dots and items
                testimonialDots.forEach(d => d.classList.remove('active'));
                testimonialItems.forEach(item => item.classList.remove('active'));

                // Add active class to current dot and item
                dot.classList.add('active');
                testimonialItems[index].classList.add('active');
            });
        });

        // Auto rotate testimonials
        let currentTestimonial = 0;
        const testimonialInterval = setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonialItems.length;

            // Remove active class from all dots and items
            testimonialDots.forEach(d => d.classList.remove('active'));
            testimonialItems.forEach(item => item.classList.remove('active'));

            // Add active class to current dot and item
            testimonialDots[currentTestimonial].classList.add('active');
            testimonialItems[currentTestimonial].classList.add('active');
        }, 6000);

        // Pause rotation on hover
        const testimonialSlider = document.querySelector('.testimonial-slider');
        if (testimonialSlider) {
            testimonialSlider.addEventListener('mouseenter', () => {
                clearInterval(testimonialInterval);
            });
        }
    }

    // ===== Trust Metrics Counter =====
    const trustMetrics = document.querySelectorAll('.metric-number');
    if (trustMetrics.length > 0) {
        const animateMetrics = () => {
            trustMetrics.forEach(metric => {
                const target = parseInt(metric.getAttribute('data-count'));
                let current = 0;
                const increment = target > 1000 ? target / 100 : target / 50;
                const duration = 2000; // 2 seconds
                const stepTime = duration / (target / increment);

                const counter = setInterval(() => {
                    current += increment;

                    // Format number with commas for thousands
                    const formattedNumber = Math.floor(current).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

                    metric.textContent = formattedNumber;

                    if (current >= target) {
                        metric.textContent = target.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                        clearInterval(counter);
                    }
                }, stepTime);
            });
        };

        // Use Intersection Observer to trigger animation when metrics are in view
        const metricsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateMetrics();
                    metricsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        const metricsSection = document.querySelector('.trust-metrics');
        if (metricsSection) {
            metricsObserver.observe(metricsSection);
        }
    }

    // ===== Carousel Functionality =====
    const carousel = document.querySelector('.carousel');
    if (carousel) {
        const slides = document.querySelectorAll('.carousel-slide');
        const dots = document.querySelectorAll('.dot');
        const prevBtn = document.querySelector('.carousel-prev');
        const nextBtn = document.querySelector('.carousel-next');
        let currentSlide = 0;
        let carouselTouchStartX = 0;
        let carouselTouchEndX = 0;
        let isSwiping = false;

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            currentSlide = (index + slides.length) % slides.length;
            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function prevSlide() {
            showSlide(currentSlide - 1);
        }

        // Event listeners for carousel controls
        if (prevBtn && nextBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                prevSlide();
                resetAutoAdvance();
            });

            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                nextSlide();
                resetAutoAdvance();
            });
        }

        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
                resetAutoAdvance();
            });
        });

        // Touch events for carousel swipe
        carousel.addEventListener('touchstart', (e) => {
            carouselTouchStartX = e.changedTouches[0].screenX;
            isSwiping = true;
            clearInterval(autoAdvanceTimer);
        }, { passive: true });

        carousel.addEventListener('touchend', (e) => {
            if (!isSwiping) return;

            carouselTouchEndX = e.changedTouches[0].screenX;
            handleCarouselSwipe();
            isSwiping = false;
            resetAutoAdvance();
        }, { passive: true });

        function handleCarouselSwipe() {
            const swipeThreshold = 50;

            // Swipe left (next slide)
            if (carouselTouchStartX - carouselTouchEndX > swipeThreshold) {
                nextSlide();
            }

            // Swipe right (previous slide)
            if (carouselTouchEndX - carouselTouchStartX > swipeThreshold) {
                prevSlide();
            }
        }

        // Auto-advance carousel with smooth transitions
        const autoAdvanceInterval = 5000; // 6 seconds between slides
        let autoAdvanceTimer;

        function startAutoAdvance() {
            // Clear any existing timer first
            if (autoAdvanceTimer) {
                clearInterval(autoAdvanceTimer);
            }

            // Set new timer
            autoAdvanceTimer = setInterval(() => {
                nextSlide();
                // Add a class to trigger a smooth transition
                slides[currentSlide].classList.add('smooth-transition');

                // Remove the class after the transition completes
                setTimeout(() => {
                    slides[currentSlide].classList.remove('smooth-transition');
                }, 1000);
            }, autoAdvanceInterval);
        }

        function resetAutoAdvance() {
            clearInterval(autoAdvanceTimer);
            startAutoAdvance();
        }

        // Start the auto-advance when the page loads
        startAutoAdvance();

        // Pause auto-advance on hover (desktop only)
        carousel.addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceTimer);
        });

        carousel.addEventListener('mouseleave', () => {
            startAutoAdvance();
        });

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                clearInterval(autoAdvanceTimer);
            } else {
                startAutoAdvance();
            }
        });
    }

    // ===== Back to Top Button =====
    const backToTopBtn = document.querySelector('.back-to-top');
    if (backToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // ===== Stats Counter Animation =====
    const stats = document.querySelectorAll('.stat-number');
    if (stats.length > 0) {
        let animated = false;

        function animateStats() {
            if (animated) return;

            stats.forEach(stat => {
                const target = parseInt(stat.getAttribute('data-count'));
                let current = 0;
                const increment = target / 50; // Adjust speed here
                const timer = setInterval(() => {
                    current += increment;
                    stat.textContent = Math.floor(current);
                    if (current >= target) {
                        stat.textContent = target;
                        clearInterval(timer);
                    }
                }, 30);
            });

            animated = true;
        }

        // Trigger stats animation when in view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                }
            });
        });

        const statsSection = document.querySelector('.intro-stats');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Also observe plant stats if they exist
        const plantStatsSection = document.querySelector('.plant-stats');
        if (plantStatsSection) {
            observer.observe(plantStatsSection);
        }
    }

    // ===== Footer Form Submission Handling =====
    const footerContactForm = document.querySelector('.footer .contact-form');
    if (footerContactForm) {
        footerContactForm.addEventListener('submit', (e) => {
            e.preventDefault();
            // Add form submission logic here
            alert('Thank you for your message! We will get back to you soon.');
            footerContactForm.reset();
        });
    }

    // ===== FAQ Accordion =====
    const faqQuestions = document.querySelectorAll('.faq-question');

    // FAQ Accordion functionality
    if (faqQuestions.length > 0) {
        faqQuestions.forEach(question => {
            question.addEventListener('click', () => {
                const faqItem = question.parentElement;
                const isActive = faqItem.classList.contains('active');

                // Close all FAQ items
                document.querySelectorAll('.faq-item').forEach(item => {
                    item.classList.remove('active');
                    const toggle = item.querySelector('.faq-toggle i');
                    if (toggle) {
                        toggle.className = 'fas fa-plus';
                    }
                });

                // Open the clicked item if it wasn't already active
                if (!isActive) {
                    faqItem.classList.add('active');
                    const toggle = question.querySelector('.faq-toggle i');
                    if (toggle) {
                        toggle.className = 'fas fa-minus';
                    }
                }
            });
        });

        // Add animation to FAQ items when they come into view
        const faqObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animated');
                    }, index * 100);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.faq-item').forEach(item => {
            faqObserver.observe(item);
        });
    }

    // ===== Scroll Animations =====
    // Animate features in Why Choose Us section
    const features = document.querySelectorAll('.feature');
    if (features.length > 0) {
        const animateFeatures = () => {
            features.forEach((feature, index) => {
                const featurePosition = feature.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.2;

                if (featurePosition < screenPosition) {
                    setTimeout(() => {
                        feature.classList.add('animated');
                    }, 150 * index);
                }
            });
        };

        window.addEventListener('scroll', animateFeatures);
        setTimeout(animateFeatures, 500); // Check on initial load
    }

    // Animate products in Products section
    const productItems = document.querySelectorAll('.product-item');
    if (productItems.length > 0) {
        const animateProducts = () => {
            productItems.forEach((item, index) => {
                const itemPosition = item.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.2;

                if (itemPosition < screenPosition) {
                    setTimeout(() => {
                        item.classList.add('animated');
                    }, 100 * index);
                }
            });
        };

        window.addEventListener('scroll', animateProducts);
        setTimeout(animateProducts, 500); // Check on initial load
    }
});
