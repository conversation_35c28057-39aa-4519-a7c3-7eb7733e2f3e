/**
 * Carousel Module
 * Handles all carousel/slider functionality
 */

/**
 * Initialize carousel functionality
 * @param {Object} options - Configuration options
 * @param {number} options.autoAdvanceInterval - Time between auto-advances in ms
 * @param {boolean} options.pauseOnHover - Whether to pause on hover
 */
export function initCarousel(options = {}) {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;
    
    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.carousel-prev');
    const nextBtn = document.querySelector('.carousel-next');
    
    if (!slides.length) return;
    
    let currentSlide = 0;
    let touchStartX = 0;
    let touchEndX = 0;
    let isSwiping = false;
    let autoAdvanceTimer = null;
    
    // Default options
    const defaultOptions = {
        autoAdvanceInterval: 5000,
        pauseOnHover: true
    };
    
    // Merge default options with provided options
    const settings = { ...defaultOptions, ...options };

    /**
     * Show a specific slide
     * @param {number} index - Index of the slide to show
     */
    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        currentSlide = (index + slides.length) % slides.length;
        slides[currentSlide].classList.add('active');
        
        if (dots.length) {
            dots[currentSlide].classList.add('active');
        }
    }

    /**
     * Go to next slide
     */
    function nextSlide() {
        showSlide(currentSlide + 1);
    }

    /**
     * Go to previous slide
     */
    function prevSlide() {
        showSlide(currentSlide - 1);
    }

    /**
     * Handle swipe gesture
     */
    function handleSwipe() {
        const swipeThreshold = 50;

        // Swipe left (next slide)
        if (touchStartX - touchEndX > swipeThreshold) {
            nextSlide();
        }

        // Swipe right (previous slide)
        if (touchEndX - touchStartX > swipeThreshold) {
            prevSlide();
        }
    }

    /**
     * Start auto-advance timer
     */
    function startAutoAdvance() {
        if (settings.autoAdvanceInterval > 0) {
            clearInterval(autoAdvanceTimer);
            autoAdvanceTimer = setInterval(nextSlide, settings.autoAdvanceInterval);
        }
    }

    /**
     * Reset auto-advance timer
     */
    function resetAutoAdvance() {
        clearInterval(autoAdvanceTimer);
        startAutoAdvance();
    }

    // Event listeners for carousel controls
    if (prevBtn && nextBtn) {
        prevBtn.addEventListener('click', (e) => {
            e.preventDefault();
            prevSlide();
            resetAutoAdvance();
        });

        nextBtn.addEventListener('click', (e) => {
            e.preventDefault();
            nextSlide();
            resetAutoAdvance();
        });
    }

    // Event listeners for dots
    if (dots.length) {
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
                resetAutoAdvance();
            });
        });
    }

    // Touch events for mobile swipe
    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        isSwiping = true;
        clearInterval(autoAdvanceTimer);
    }, { passive: true });

    carousel.addEventListener('touchend', (e) => {
        if (!isSwiping) return;

        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
        isSwiping = false;
        resetAutoAdvance();
    }, { passive: true });

    // Pause auto-advance on hover (desktop only)
    if (settings.pauseOnHover) {
        carousel.addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceTimer);
        });

        carousel.addEventListener('mouseleave', () => {
            startAutoAdvance();
        });
    }

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            clearInterval(autoAdvanceTimer);
        } else {
            startAutoAdvance();
        }
    });

    // Initialize
    showSlide(0);
    startAutoAdvance();
}

export default { initCarousel };
