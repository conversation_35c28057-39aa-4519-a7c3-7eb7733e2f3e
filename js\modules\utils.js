/**
 * Utility functions for use across modules
 */

/**
 * Throttle function to limit execution frequency
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in ms
 * @returns {Function} Throttled function
 */
export function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Debounce function to delay execution until after a pause
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in ms
 * @returns {Function} Debounced function
 */
export function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

/**
 * Check if an element is in viewport
 * @param {HTMLElement} element - Element to check
 * @param {number} offset - Offset from viewport edge
 * @returns {boolean} Whether element is in viewport
 */
export function isInViewport(element, offset = 0) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top <= (window.innerHeight - offset) &&
        rect.bottom >= 0
    );
}

/**
 * Lazy load images
 * @param {string} selector - CSS selector for images to lazy load
 */
export function lazyLoadImages(selector = 'img[data-src]') {
    const images = document.querySelectorAll(selector);
    if (!images.length) return;
    
    // Use Intersection Observer for better performance
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.getAttribute('data-src');
                    
                    if (src) {
                        img.src = src;
                        img.removeAttribute('data-src');
                    }
                    
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        const lazyLoadThrottled = throttle(() => {
            images.forEach(img => {
                if (isInViewport(img, 300)) {
                    const src = img.getAttribute('data-src');
                    
                    if (src) {
                        img.src = src;
                        img.removeAttribute('data-src');
                    }
                }
            });
            
            // If all images are loaded, remove the scroll event listener
            if ([...images].every(img => !img.hasAttribute('data-src'))) {
                window.removeEventListener('scroll', lazyLoadThrottled);
            }
        }, 200);
        
        window.addEventListener('scroll', lazyLoadThrottled);
        lazyLoadThrottled(); // Initial check
    }
}

/**
 * Add preload hints for resources
 * @param {Array} resources - Array of resource objects with type and url
 */
export function addPreloadHints(resources) {
    if (!resources || !resources.length) return;
    
    resources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = resource.rel || 'preload';
        link.href = resource.url;
        link.as = resource.type;
        
        if (resource.crossorigin) {
            link.crossOrigin = resource.crossorigin;
        }
        
        document.head.appendChild(link);
    });
}

export default {
    throttle,
    debounce,
    isInViewport,
    lazyLoadImages,
    addPreloadHints
};
