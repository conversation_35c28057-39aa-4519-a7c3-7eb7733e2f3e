// Navbar scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('header');
    if (window.scrollY > 50) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }

    // Highlight active section in navbar
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-links li');

    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.querySelector('a').getAttribute('href') === '#' + sectionId) {
                    link.classList.add('active');
                }
            });
        }
    });
});

// Mobile menu functionality
document.addEventListener('DOMContentLoaded', () => {
    const nav = document.querySelector('.main-nav');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const menuItems = navLinks.querySelectorAll('a');

    // Add smooth scrolling to all navigation links
    menuItems.forEach(link => {
        if (link.getAttribute('href').startsWith('#')) {
            link.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href');
                if (targetId !== '#') {
                    e.preventDefault();
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        const offsetTop = targetElement.offsetTop - 80;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        }
    });

    // Toggle mobile menu
    mobileMenuBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent event bubbling
        navLinks.classList.toggle('show');
        mobileMenuBtn.innerHTML = navLinks.classList.contains('show')
            ? '<i class="fas fa-times"></i>'
            : '<i class="fas fa-bars"></i>';
        document.body.style.overflow = navLinks.classList.contains('show') ? 'hidden' : '';
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!nav.contains(e.target) && navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.style.overflow = '';
        }
    });

    // Close menu when clicking on menu items
    menuItems.forEach(item => {
        item.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                navLinks.classList.remove('show');
                mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                document.body.style.overflow = '';
            }
        });
    });

    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768 && navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.style.overflow = '';
        }
    });

    // Add touch support for mobile devices
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
    }, false);

    document.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, false);

    function handleSwipe() {
        // Swipe right to open menu
        if (touchEndX - touchStartX > 100 && !navLinks.classList.contains('show')) {
            navLinks.classList.add('show');
            mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
            document.body.style.overflow = 'hidden';
        }

        // Swipe left to close menu
        if (touchStartX - touchEndX > 100 && navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.style.overflow = '';
        }
    }
});

// Form submission handling
const contactForm = document.querySelector('.contact-form');
if (contactForm) {
    contactForm.addEventListener('submit', (e) => {
        e.preventDefault();
        // Add form submission logic here
        alert('Thank you for your message! We will get back to you soon.');
        contactForm.reset();
    });
}

// Back to top button functionality
document.addEventListener('DOMContentLoaded', () => {
    const backToTopButton = document.querySelector('.back-to-top');

    if (backToTopButton) {
        // Show/hide back to top button based on scroll position
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopButton.classList.add('show');
            } else {
                backToTopButton.classList.remove('show');
            }
        });

        // Smooth scroll to top when clicking the button
        backToTopButton.addEventListener('click', (e) => {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Why Choose Us section animations
    const whyUsSection = document.querySelector('.why-us');
    const features = document.querySelectorAll('.feature');

    if (whyUsSection && features.length) {
        const animateFeatures = () => {
            const whyUsPosition = whyUsSection.getBoundingClientRect().top;
            const screenPosition = window.innerHeight / 1.2;

            if (whyUsPosition < screenPosition) {
                features.forEach((feature, index) => {
                    // Add staggered animation delay
                    setTimeout(() => {
                        feature.style.opacity = '1';
                        feature.style.transform = 'translateY(0)';
                    }, 200 * index);
                });

                // Remove scroll listener once animation is triggered
                window.removeEventListener('scroll', animateFeatures);
            }
        };

        // Set initial state
        features.forEach(feature => {
            feature.style.opacity = '0';
            feature.style.transform = 'translateY(30px)';
            feature.style.transition = 'all 0.6s ease-out';
        });

        // Add scroll listener
        window.addEventListener('scroll', animateFeatures);

        // Check on initial load
        setTimeout(animateFeatures, 500);
    }

    // Products section animations
    const productsSection = document.querySelector('.products');
    const productItems = document.querySelectorAll('.product-item');

    if (productsSection && productItems.length) {
        const animateProducts = () => {
            const productsPosition = productsSection.getBoundingClientRect().top;
            const screenPosition = window.innerHeight / 1.2;

            if (productsPosition < screenPosition) {
                productItems.forEach((item, index) => {
                    // Add staggered animation delay
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 150 * index);
                });

                // Remove scroll listener once animation is triggered
                window.removeEventListener('scroll', animateProducts);
            }
        };

        // Set initial state
        productItems.forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(30px)';
            item.style.transition = 'all 0.6s ease-out';
        });

        // Add scroll listener
        window.addEventListener('scroll', animateProducts);

        // Check on initial load
        setTimeout(animateProducts, 500);
    }

    // Plant section parallax and animations
    const plantSection = document.querySelector('.plant-info');
    const plantFeatures = document.querySelectorAll('.plant-feature');

    if (plantSection) {
        // Parallax effect for plant section
        // This will work once you add your own background image
        window.addEventListener('scroll', () => {
            const scrollPosition = window.scrollY;
            const plantPosition = plantSection.offsetTop;
            const windowHeight = window.innerHeight;

            // Only apply parallax when plant section is in view
            if (scrollPosition + windowHeight > plantPosition &&
                scrollPosition < plantPosition + plantSection.offsetHeight) {

                // Uncomment the line below after adding your background image
                // const parallaxOffset = (scrollPosition - plantPosition) * 0.4;
                // plantSection.style.backgroundPositionY = `calc(50% + ${parallaxOffset}px)`;
            }
        });

        // Animate plant features on scroll
        const animateOnScroll = () => {
            plantFeatures.forEach((feature, index) => {
                const featurePosition = feature.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;

                if (featurePosition < screenPosition) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        feature.style.opacity = '1';
                        feature.style.transform = 'translateY(0)';
                    }, index * 200);
                }
            });
        };

        // Set initial state for animations
        plantFeatures.forEach(feature => {
            feature.style.opacity = '0';
            feature.style.transform = 'translateY(30px)';
            feature.style.transition = 'all 0.6s ease-out';
        });

        // Run animation check on scroll
        window.addEventListener('scroll', animateOnScroll);

        // Run once on page load
        setTimeout(animateOnScroll, 500);
    }
});