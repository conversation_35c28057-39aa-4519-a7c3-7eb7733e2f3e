<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Test - EDEN Pharmaceuticals</title>
    
    <!-- Resource hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Optimized CSS -->
    <link rel="stylesheet" href="css/optimized.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.onload=null;this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>
    
    <!-- Optimized JavaScript -->
    <script src="js/optimized.js" defer></script>
    
    <style>
        .test-section {
            padding: 2rem;
            margin: 2rem 0;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            margin-bottom: 1rem;
            color: #0056b3;
        }
        
        .test-description {
            margin-bottom: 2rem;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        
        .success {
            color: green;
            font-weight: bold;
        }
        
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <header>
            <nav class="main-nav">
                <button class="mobile-menu-btn"><i class="fas fa-bars"></i></button>
                <div class="logo">EDEN</div>
                <ul class="nav-links">
                    <li class="active"><a href="#">Test</a></li>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                </ul>
            </nav>
        </header>
        
        <main style="margin-top: 100px; padding: 20px;">
            <div class="container">
                <h1>Animation and Style Test Page</h1>
                <p>This page tests if all animations and styles are working correctly.</p>
                
                <!-- Carousel Test -->
                <section class="test-section">
                    <h2 class="test-title">1. Carousel Animation Test</h2>
                    <p class="test-description">Testing carousel animations and transitions:</p>
                    
                    <div class="carousel-container" style="height: 400px; margin: 0;">
                        <div class="carousel">
                            <div class="carousel-slide active">
                                <img src="https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=80" alt="Test Slide 1">
                                <div class="carousel-caption">
                                    <h1>TEST <span class="highlight">SLIDE 1</span></h1>
                                    <h2>TESTING <span class="highlight">ANIMATIONS</span></h2>
                                </div>
                            </div>
                            <div class="carousel-slide">
                                <img src="https://images.unsplash.com/photo-1628595351029-c2bf17511435?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=80" alt="Test Slide 2">
                                <div class="carousel-caption">
                                    <h1>TEST <span class="highlight">SLIDE 2</span></h1>
                                    <h2>TESTING <span class="highlight">ANIMATIONS</span></h2>
                                </div>
                            </div>
                            
                            <!-- Carousel Navigation -->
                            <div class="carousel-nav">
                                <button class="carousel-prev" aria-label="Previous slide"><i class="fas fa-chevron-left"></i></button>
                                <div class="carousel-dots">
                                    <span class="dot active" data-slide="0" aria-label="Slide 1"></span>
                                    <span class="dot" data-slide="1" aria-label="Slide 2"></span>
                                </div>
                                <button class="carousel-next" aria-label="Next slide"><i class="fas fa-chevron-right"></i></button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="carousel-result">
                        <p>Carousel should show animations for:</p>
                        <ul>
                            <li>Slide transitions</li>
                            <li>Caption text fade-in and slide-up</li>
                            <li>Highlight underline animation</li>
                            <li>Navigation controls fade-in</li>
                        </ul>
                    </div>
                </section>
                
                <!-- Feature Animation Test -->
                <section class="test-section">
                    <h2 class="test-title">2. Feature Animation Test</h2>
                    <p class="test-description">Testing feature animations on scroll:</p>
                    
                    <div class="features">
                        <div class="feature">
                            <div class="feature-icon-wrapper">
                                <i class="fas fa-flask"></i>
                            </div>
                            <h3>Test Feature 1</h3>
                            <p>This feature should animate when scrolled into view</p>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon-wrapper">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <h3>Test Feature 2</h3>
                            <p>This feature should animate when scrolled into view</p>
                        </div>
                        
                        <div class="feature">
                            <div class="feature-icon-wrapper">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3>Test Feature 3</h3>
                            <p>This feature should animate when scrolled into view</p>
                        </div>
                    </div>
                    
                    <div class="test-result" id="feature-result">
                        <p>Features should fade in and slide up when scrolled into view.</p>
                    </div>
                </section>
                
                <!-- Product Animation Test -->
                <section class="test-section">
                    <h2 class="test-title">3. Product Animation Test</h2>
                    <p class="test-description">Testing product animations on scroll:</p>
                    
                    <div class="product-grid" style="grid-template-columns: repeat(3, 1fr);">
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Test Product">
                                </div>
                                <div class="product-content">
                                    <h3>Test Product 1</h3>
                                    <p>This product should animate when scrolled into view</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Test Product">
                                </div>
                                <div class="product-content">
                                    <h3>Test Product 2</h3>
                                    <p>This product should animate when scrolled into view</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1550572017-edd951b55104?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Test Product">
                                </div>
                                <div class="product-content">
                                    <h3>Test Product 3</h3>
                                    <p>This product should animate when scrolled into view</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="product-result">
                        <p>Products should fade in and slide up when scrolled into view.</p>
                    </div>
                </section>
                
                <!-- Stats Counter Test -->
                <section class="test-section">
                    <h2 class="test-title">4. Stats Counter Test</h2>
                    <p class="test-description">Testing stats counter animations:</p>
                    
                    <div class="intro-stats">
                        <div class="stat-item">
                            <div class="stat-number" data-count="100">0</div>
                            <div class="stat-label">Test Stat 1</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="250">0</div>
                            <div class="stat-label">Test Stat 2</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="50">0</div>
                            <div class="stat-label">Test Stat 3</div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="stats-result">
                        <p>Stats should count up from 0 to their target values when scrolled into view.</p>
                    </div>
                </section>
            </div>
        </main>
        
        <footer>
            <div class="footer-content">
                <div class="copyright">
                    <p>Test Footer</p>
                </div>
            </div>
        </footer>
    </div>
    
    <a href="#" class="back-to-top" aria-label="Back to top"><i class="fas fa-arrow-up"></i></a>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add scroll event listener to check animations
            window.addEventListener('scroll', checkAnimations);
            
            // Initial check
            setTimeout(checkAnimations, 1000);
            
            function checkAnimations() {
                // Check feature animations
                const features = document.querySelectorAll('.feature');
                let featuresAnimated = true;
                features.forEach(feature => {
                    if (!feature.classList.contains('animated')) {
                        featuresAnimated = false;
                    }
                });
                
                if (featuresAnimated && features.length > 0) {
                    document.getElementById('feature-result').innerHTML += '<p class="success">✓ Feature animations are working correctly!</p>';
                }
                
                // Check product animations
                const products = document.querySelectorAll('.product-item');
                let productsAnimated = true;
                products.forEach(product => {
                    if (!product.classList.contains('animated')) {
                        productsAnimated = false;
                    }
                });
                
                if (productsAnimated && products.length > 0) {
                    document.getElementById('product-result').innerHTML += '<p class="success">✓ Product animations are working correctly!</p>';
                }
                
                // Check stats counter
                const stats = document.querySelectorAll('.stat-number');
                let statsAnimated = true;
                stats.forEach(stat => {
                    if (stat.textContent === '0') {
                        statsAnimated = false;
                    }
                });
                
                if (statsAnimated && stats.length > 0) {
                    document.getElementById('stats-result').innerHTML += '<p class="success">✓ Stats counter is working correctly!</p>';
                }
                
                // Remove event listener if all animations are checked
                if (featuresAnimated && productsAnimated && statsAnimated) {
                    window.removeEventListener('scroll', checkAnimations);
                }
            }
        });
    </script>
</body>
</html>
