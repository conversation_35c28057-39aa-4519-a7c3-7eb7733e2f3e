/* Critical CSS for above-the-fold content */

/* Variables */
:root {
    --primary-color: #0056b3;
    --accent-color: #00a0e9;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 5%;
}

/* Header and Navigation */
header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
}

header.scrolled {
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    position: relative;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 1.5rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 0;
}

.nav-links a:hover,
.nav-links li.active a {
    color: var(--primary-color);
}

.nav-links li.active a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.contact-btn {
    background-color: var(--primary-color);
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 4px;
}

.contact-btn:hover {
    background-color: var(--accent-color);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    overflow: hidden;
    margin-top: -70px; /* Offset the header height */
}

.carousel {
    height: 100%;
    position: relative;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 1s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-slide.active {
    opacity: 1;
    z-index: 1;
}

.carousel-caption {
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 2rem;
    z-index: 2;
}

.carousel-caption h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.carousel-caption h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1;
}

/* Responsive styles for critical elements */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .nav-links.show {
        display: flex;
    }

    .nav-links li {
        margin: 0.5rem 0;
    }

    .carousel-caption h1 {
        font-size: 2.5rem;
    }

    .carousel-caption h2 {
        font-size: 1.5rem;
    }
}
