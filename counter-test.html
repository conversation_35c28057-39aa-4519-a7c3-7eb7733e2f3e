<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Counter Test - EDEN Pharmaceuticals</title>
    
    <!-- Resource hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Optimized CSS -->
    <link rel="stylesheet" href="css/optimized.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.onload=null;this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>
    
    <!-- Optimized JavaScript -->
    <script src="js/optimized.js" defer></script>
    
    <style>
        .test-section {
            padding: 2rem;
            margin: 2rem 0;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            margin-bottom: 1rem;
            color: #0056b3;
        }
        
        .test-description {
            margin-bottom: 2rem;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        
        .success {
            color: green;
            font-weight: bold;
        }
        
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <header>
            <nav class="main-nav">
                <button class="mobile-menu-btn"><i class="fas fa-bars"></i></button>
                <div class="logo">EDEN</div>
                <ul class="nav-links">
                    <li class="active"><a href="#">Test</a></li>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="plant.html">Our Plant</a></li>
                </ul>
            </nav>
        </header>
        
        <main style="margin-top: 100px; padding: 20px;">
            <div class="container">
                <h1>Counter Animation Test Page</h1>
                <p>This page tests if all counter animations are working correctly.</p>
                
                <!-- Home Page Counter Test -->
                <section class="test-section">
                    <h2 class="test-title">1. Home Page Stats Counter Test</h2>
                    <p class="test-description">Testing the stats counter from the home page:</p>
                    
                    <div class="intro-stats">
                        <div class="stat-item">
                            <div class="stat-number" data-count="100">0</div>
                            <div class="stat-label">Test Stat 1</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="250">0</div>
                            <div class="stat-label">Test Stat 2</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="50">0</div>
                            <div class="stat-label">Test Stat 3</div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="home-stats-result">
                        <p>Home page stats should count up from 0 to their target values when scrolled into view.</p>
                    </div>
                </section>
                
                <!-- About Page Achievement Counter Test -->
                <section class="test-section">
                    <h2 class="test-title">2. About Page Achievement Counter Test</h2>
                    <p class="test-description">Testing the achievement counters from the about page:</p>
                    
                    <div class="company-achievements">
                        <div class="achievement-item">
                            <div class="achievement-icon"><i class="fas fa-award"></i></div>
                            <div class="achievement-number" data-count="25">0</div>
                            <div class="achievement-text">Years of Excellence</div>
                        </div>
                        <div class="achievement-item">
                            <div class="achievement-icon"><i class="fas fa-microscope"></i></div>
                            <div class="achievement-number" data-count="50">0</div>
                            <div class="achievement-text">Research Patents</div>
                        </div>
                        <div class="achievement-item">
                            <div class="achievement-icon"><i class="fas fa-globe-americas"></i></div>
                            <div class="achievement-number" data-count="40">0</div>
                            <div class="achievement-text">Countries Served</div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="achievement-result">
                        <p>Achievement counters should count up from 0 to their target values when scrolled into view.</p>
                    </div>
                </section>
                
                <!-- About Page Trust Metrics Counter Test -->
                <section class="test-section">
                    <h2 class="test-title">3. About Page Trust Metrics Counter Test</h2>
                    <p class="test-description">Testing the trust metrics counters from the about page:</p>
                    
                    <div class="trust-metrics">
                        <div class="metric-item">
                            <div class="metric-icon"><i class="fas fa-hospital"></i></div>
                            <div class="metric-number" data-count="500">0</div>
                            <div class="metric-label">Healthcare Facilities</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-icon"><i class="fas fa-handshake"></i></div>
                            <div class="metric-number" data-count="75">0</div>
                            <div class="metric-label">Strategic Partnerships</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-icon"><i class="fas fa-certificate"></i></div>
                            <div class="metric-number" data-count="30">0</div>
                            <div class="metric-label">Industry Certifications</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-icon"><i class="fas fa-users"></i></div>
                            <div class="metric-number" data-count="1000000">0</div>
                            <div class="metric-label">Patients Helped Annually</div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="trust-metrics-result">
                        <p>Trust metrics counters should count up from 0 to their target values when scrolled into view.</p>
                    </div>
                </section>
                
                <!-- Plant Page Stats Counter Test -->
                <section class="test-section">
                    <h2 class="test-title">4. Plant Page Stats Counter Test</h2>
                    <p class="test-description">Testing the plant stats counters from the plant page:</p>
                    
                    <div class="plant-stats">
                        <div class="stat-item">
                            <div class="stat-number" data-count="50000">0</div>
                            <div class="stat-label">Square Feet</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="24">0</div>
                            <div class="stat-label">Hour Operation</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="5">0</div>
                            <div class="stat-label">Production Lines</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="100">0</div>
                            <div class="stat-label">Million Units/Year</div>
                        </div>
                    </div>
                    
                    <div class="test-result" id="plant-stats-result">
                        <p>Plant stats counters should count up from 0 to their target values when scrolled into view.</p>
                    </div>
                </section>
            </div>
        </main>
        
        <footer>
            <div class="footer-content">
                <div class="copyright">
                    <p>Test Footer</p>
                </div>
            </div>
        </footer>
    </div>
    
    <a href="#" class="back-to-top" aria-label="Back to top"><i class="fas fa-arrow-up"></i></a>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add scroll event listener to check animations
            window.addEventListener('scroll', checkCounters);
            
            // Initial check
            setTimeout(checkCounters, 1000);
            
            function checkCounters() {
                // Check home stats counters
                const homeStats = document.querySelectorAll('.intro-stats .stat-number');
                let homeStatsAnimated = true;
                homeStats.forEach(stat => {
                    if (stat.textContent === '0') {
                        homeStatsAnimated = false;
                    }
                });
                
                if (homeStatsAnimated && homeStats.length > 0) {
                    document.getElementById('home-stats-result').innerHTML += '<p class="success">✓ Home page stats counters are working correctly!</p>';
                }
                
                // Check achievement counters
                const achievements = document.querySelectorAll('.company-achievements .achievement-number');
                let achievementsAnimated = true;
                achievements.forEach(achievement => {
                    if (achievement.textContent === '0') {
                        achievementsAnimated = false;
                    }
                });
                
                if (achievementsAnimated && achievements.length > 0) {
                    document.getElementById('achievement-result').innerHTML += '<p class="success">✓ Achievement counters are working correctly!</p>';
                }
                
                // Check trust metrics counters
                const trustMetrics = document.querySelectorAll('.trust-metrics .metric-number');
                let trustMetricsAnimated = true;
                trustMetrics.forEach(metric => {
                    if (metric.textContent === '0') {
                        trustMetricsAnimated = false;
                    }
                });
                
                if (trustMetricsAnimated && trustMetrics.length > 0) {
                    document.getElementById('trust-metrics-result').innerHTML += '<p class="success">✓ Trust metrics counters are working correctly!</p>';
                }
                
                // Check plant stats counters
                const plantStats = document.querySelectorAll('.plant-stats .stat-number');
                let plantStatsAnimated = true;
                plantStats.forEach(stat => {
                    if (stat.textContent === '0') {
                        plantStatsAnimated = false;
                    }
                });
                
                if (plantStatsAnimated && plantStats.length > 0) {
                    document.getElementById('plant-stats-result').innerHTML += '<p class="success">✓ Plant stats counters are working correctly!</p>';
                }
                
                // Remove event listener if all counters are checked
                if (homeStatsAnimated && achievementsAnimated && trustMetricsAnimated && plantStatsAnimated) {
                    window.removeEventListener('scroll', checkCounters);
                }
            }
        });
    </script>
</body>
</html>
