<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Test Page</title>
    
    <!-- Resource hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Optimized CSS -->
    <link rel="stylesheet" href="css/optimized.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.onload=null;this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>
    
    <!-- Optimized JavaScript -->
    <script src="js/optimized.js" defer></script>
    
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-button {
            background-color: #0056b3;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 1rem;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f5f5f5;
            border-radius: 4px;
            min-height: 50px;
        }
        
        .success {
            color: green;
        }
        
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <header>
            <nav class="main-nav">
                <button class="mobile-menu-btn"><i class="fas fa-bars"></i></button>
                <div class="logo">EDEN</div>
                <ul class="nav-links">
                    <li class="active"><a href="#">Test</a></li>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                </ul>
            </nav>
        </header>
        
        <main style="margin-top: 100px; padding: 20px;">
            <div class="container">
                <h1>Website Functionality Test Page</h1>
                <p>This page tests if all the website functionality is working correctly after fixes.</p>
                
                <div class="test-section">
                    <h2>1. Mobile Menu Test</h2>
                    <p>Click the button below to test the mobile menu functionality:</p>
                    <button class="test-button" id="test-mobile-menu">Test Mobile Menu</button>
                    <div class="test-result" id="mobile-menu-result"></div>
                </div>
                
                <div class="test-section">
                    <h2>2. Back to Top Button Test</h2>
                    <p>Scroll down to see the back to top button appear:</p>
                    <button class="test-button" id="test-back-to-top">Test Back to Top</button>
                    <div class="test-result" id="back-to-top-result"></div>
                </div>
                
                <div class="test-section">
                    <h2>3. Stats Counter Test</h2>
                    <div class="intro-stats">
                        <div class="stat-item">
                            <div class="stat-number" data-count="100">0</div>
                            <div class="stat-label">Test Stat</div>
                        </div>
                    </div>
                    <button class="test-button" id="test-stats">Test Stats Counter</button>
                    <div class="test-result" id="stats-result"></div>
                </div>
                
                <div class="test-section">
                    <h2>4. Product Animation Test</h2>
                    <div class="product-grid" style="grid-template-columns: repeat(2, 1fr);">
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://via.placeholder.com/300" alt="Test Product">
                                </div>
                                <div class="product-content">
                                    <h3>Test Product 1</h3>
                                    <p>This is a test product</p>
                                </div>
                            </div>
                        </div>
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://via.placeholder.com/300" alt="Test Product">
                                </div>
                                <div class="product-content">
                                    <h3>Test Product 2</h3>
                                    <p>This is a test product</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="test-button" id="test-products">Test Product Animations</button>
                    <div class="test-result" id="products-result"></div>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="footer-content">
                <div class="copyright">
                    <p>Test Footer</p>
                </div>
            </div>
        </footer>
    </div>
    
    <a href="#" class="back-to-top" aria-label="Back to top"><i class="fas fa-arrow-up"></i></a>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Test Mobile Menu
            document.getElementById('test-mobile-menu').addEventListener('click', function() {
                try {
                    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
                    mobileMenuBtn.click();
                    setTimeout(() => {
                        const navLinks = document.querySelector('.nav-links');
                        if (navLinks.classList.contains('show')) {
                            document.getElementById('mobile-menu-result').innerHTML = '<span class="success">Success! Mobile menu is working correctly.</span>';
                            // Close the menu after test
                            setTimeout(() => mobileMenuBtn.click(), 1000);
                        } else {
                            document.getElementById('mobile-menu-result').innerHTML = '<span class="error">Error: Mobile menu is not showing.</span>';
                        }
                    }, 300);
                } catch (e) {
                    document.getElementById('mobile-menu-result').innerHTML = '<span class="error">Error: ' + e.message + '</span>';
                }
            });
            
            // Test Back to Top
            document.getElementById('test-back-to-top').addEventListener('click', function() {
                try {
                    const backToTop = document.querySelector('.back-to-top');
                    window.scrollTo(0, 1000);
                    setTimeout(() => {
                        if (backToTop.classList.contains('visible')) {
                            document.getElementById('back-to-top-result').innerHTML = '<span class="success">Success! Back to top button is visible after scrolling.</span>';
                            backToTop.click();
                        } else {
                            document.getElementById('back-to-top-result').innerHTML = '<span class="error">Error: Back to top button is not showing after scrolling.</span>';
                        }
                    }, 500);
                } catch (e) {
                    document.getElementById('back-to-top-result').innerHTML = '<span class="error">Error: ' + e.message + '</span>';
                }
            });
            
            // Test Stats Counter
            document.getElementById('test-stats').addEventListener('click', function() {
                try {
                    const statNumber = document.querySelector('.stat-number');
                    const initialValue = statNumber.textContent;
                    
                    // Force animation
                    if (typeof initStatsCounter === 'function') {
                        initStatsCounter();
                        setTimeout(() => {
                            const newValue = statNumber.textContent;
                            if (newValue !== initialValue && parseInt(newValue) > 0) {
                                document.getElementById('stats-result').innerHTML = '<span class="success">Success! Stats counter is animating correctly.</span>';
                            } else {
                                document.getElementById('stats-result').innerHTML = '<span class="error">Error: Stats counter is not animating.</span>';
                            }
                        }, 1000);
                    } else {
                        document.getElementById('stats-result').innerHTML = '<span class="error">Error: initStatsCounter function not found.</span>';
                    }
                } catch (e) {
                    document.getElementById('stats-result').innerHTML = '<span class="error">Error: ' + e.message + '</span>';
                }
            });
            
            // Test Product Animations
            document.getElementById('test-products').addEventListener('click', function() {
                try {
                    const productItems = document.querySelectorAll('.product-item');
                    
                    // Force animation
                    if (typeof initProductAnimations === 'function') {
                        initProductAnimations();
                        setTimeout(() => {
                            if (productItems[0].classList.contains('animated')) {
                                document.getElementById('products-result').innerHTML = '<span class="success">Success! Product animations are working correctly.</span>';
                            } else {
                                document.getElementById('products-result').innerHTML = '<span class="error">Error: Product items are not being animated.</span>';
                            }
                        }, 500);
                    } else {
                        document.getElementById('products-result').innerHTML = '<span class="error">Error: initProductAnimations function not found.</span>';
                    }
                } catch (e) {
                    document.getElementById('products-result').innerHTML = '<span class="error">Error: ' + e.message + '</span>';
                }
            });
        });
    </script>
</body>
</html>
