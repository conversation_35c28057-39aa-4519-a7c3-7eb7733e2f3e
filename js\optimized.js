/**
 * EDEN Pharmaceuticals - Optimized JavaScript
 * This file contains all necessary functionality in a single, optimized file
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Initialize back to top button
    initBackToTop();

    // Initialize product animations if products exist
    if (document.querySelectorAll('.product-item').length > 0) {
        initProductAnimations();
    }

    // Initialize feature animations if features exist
    if (document.querySelectorAll('.feature').length > 0) {
        initFeatureAnimations();
    }

    // Initialize carousel if it exists
    const carousel = document.querySelector('.carousel');
    if (carousel) {
        initCarousel({
            autoAdvanceInterval: 5000,
            pauseOnHover: true
        });
    }

    // Initialize stats counter if stats exist
    if (document.querySelectorAll('.stat-number').length > 0) {
        initStatsCounter();
    }

    // Initialize FAQ accordion if it exists
    if (document.querySelectorAll('.faq-item').length > 0) {
        initFaqAccordion();
    }

    // Initialize value items animations if they exist
    if (document.querySelectorAll('.value-item').length > 0) {
        initValueAnimations();
    }

    // Initialize team member animations if they exist
    if (document.querySelectorAll('.team-member').length > 0) {
        initTeamAnimations();
    }

    // Initialize contact form if it exists
    if (document.querySelector('.contact-form')) {
        initContactForm();
    }

    // Initialize location card animations if they exist
    if (document.querySelectorAll('.location-card, .contact-card').length > 0) {
        initLocationCardAnimations();
    }

    // Initialize plant tour parallax if it exists
    if (document.querySelector('.plant-cta')) {
        initPlantTourParallax();
    }
});

/**
 * Initialize mobile menu functionality
 */
function initMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const dropdowns = document.querySelectorAll('.dropdown');

    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            navLinks.classList.toggle('show');
            mobileMenuBtn.innerHTML = navLinks.classList.contains('show')
                ? '<i class="fas fa-times"></i>'
                : '<i class="fas fa-bars"></i>';
            document.body.style.overflow = navLinks.classList.contains('show') ? 'hidden' : '';
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && !navLinks.contains(e.target) && e.target !== mobileMenuBtn) {
                navLinks.classList.remove('show');
                mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                document.body.style.overflow = '';
            }
        });

        // Handle dropdown menus on mobile
        dropdowns.forEach(dropdown => {
            const dropdownLink = dropdown.querySelector('a');

            dropdownLink.addEventListener('click', function(e) {
                // Only prevent default on mobile view
                if (window.innerWidth <= 768) {
                    e.preventDefault();
                    dropdown.classList.toggle('active');

                    // Close other dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                }
            });
        });

        // Reset dropdowns on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
}

/**
 * Initialize back to top button with throttled scroll event
 */
function initBackToTop() {
    const backToTop = document.querySelector('.back-to-top');
    if (!backToTop) return;

    // Throttle scroll event for better performance
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(function() {
                if (window.pageYOffset > 100) {
                    backToTop.classList.add('visible');
                } else {
                    backToTop.classList.remove('visible');
                }
                scrollTimeout = null;
            }, 100);
        }
    }, { passive: true });

    backToTop.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

/**
 * Initialize product animations with Intersection Observer
 */
function initProductAnimations() {
    const productItems = document.querySelectorAll('.product-item');

    if (productItems.length && 'IntersectionObserver' in window) {
        const productObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    productObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1, rootMargin: '50px' });

        productItems.forEach(item => {
            productObserver.observe(item);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        productItems.forEach(item => {
            item.classList.add('animated');
        });
    }
}

/**
 * Initialize carousel functionality
 * @param {Object} options - Configuration options
 */
function initCarousel(options = {}) {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;

    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.carousel-prev');
    const nextBtn = document.querySelector('.carousel-next');

    if (!slides.length) return;

    let currentSlide = 0;
    let autoAdvanceTimer = null;

    // Default options
    const settings = {
        autoAdvanceInterval: 5000,
        pauseOnHover: true,
        ...options
    };

    /**
     * Show a specific slide
     * @param {number} index - Index of the slide to show
     */
    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        currentSlide = (index + slides.length) % slides.length;
        slides[currentSlide].classList.add('active');

        if (dots.length) {
            dots[currentSlide].classList.add('active');
        }
    }

    /**
     * Start auto-advance timer
     */
    function startAutoAdvance() {
        if (settings.autoAdvanceInterval > 0) {
            clearInterval(autoAdvanceTimer);
            autoAdvanceTimer = setInterval(() => {
                showSlide(currentSlide + 1);
            }, settings.autoAdvanceInterval);
        }
    }

    /**
     * Reset auto-advance timer
     */
    function resetAutoAdvance() {
        clearInterval(autoAdvanceTimer);
        startAutoAdvance();
    }

    // Event listeners for carousel controls
    if (prevBtn && nextBtn) {
        prevBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showSlide(currentSlide - 1);
            resetAutoAdvance();
        });

        nextBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showSlide(currentSlide + 1);
            resetAutoAdvance();
        });
    }

    // Event listeners for dots
    if (dots.length) {
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
                resetAutoAdvance();
            });
        });
    }

    // Touch events for mobile swipe
    let touchStartX = 0;
    let touchEndX = 0;

    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        clearInterval(autoAdvanceTimer);
    }, { passive: true });

    carousel.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        const swipeThreshold = 50;

        // Swipe left (next slide)
        if (touchStartX - touchEndX > swipeThreshold) {
            showSlide(currentSlide + 1);
        }

        // Swipe right (previous slide)
        if (touchEndX - touchStartX > swipeThreshold) {
            showSlide(currentSlide - 1);
        }

        resetAutoAdvance();
    }, { passive: true });

    // Pause auto-advance on hover (desktop only)
    if (settings.pauseOnHover) {
        carousel.addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceTimer);
        });

        carousel.addEventListener('mouseleave', () => {
            startAutoAdvance();
        });
    }

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            clearInterval(autoAdvanceTimer);
        } else {
            startAutoAdvance();
        }
    });

    // Initialize
    showSlide(0);
    startAutoAdvance();
}

/**
 * Initialize feature animations with Intersection Observer
 */
function initFeatureAnimations() {
    const features = document.querySelectorAll('.feature');

    if (features.length && 'IntersectionObserver' in window) {
        const featureObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    featureObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1, rootMargin: '50px' });

        features.forEach(feature => {
            featureObserver.observe(feature);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        features.forEach(feature => {
            feature.classList.add('animated');
        });
    }
}

/**
 * Initialize FAQ accordion functionality
 */
function initFaqAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
            // Toggle active class on the clicked item
            item.classList.toggle('active');

            // Close other items
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
        });
    });
}

/**
 * Initialize value items animations with Intersection Observer
 */
function initValueAnimations() {
    const valueItems = document.querySelectorAll('.value-item');

    if (valueItems.length && 'IntersectionObserver' in window) {
        const valueObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 150);

                    valueObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        valueItems.forEach(item => {
            // Set initial styles
            item.style.opacity = '0';
            item.style.transform = 'translateY(30px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

            valueObserver.observe(item);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        valueItems.forEach(item => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        });
    }
}

/**
 * Initialize contact form functionality
 */
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    const formSuccess = document.querySelector('.form-success');
    const sendAnother = document.getElementById('sendAnother');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission (in a real application, you would send the data to a server)
            setTimeout(() => {
                contactForm.style.display = 'none';
                formSuccess.style.display = 'block';
            }, 1000);
        });
    }

    if (sendAnother) {
        sendAnother.addEventListener('click', function() {
            formSuccess.style.display = 'none';
            contactForm.style.display = 'flex';
            contactForm.reset();
        });
    }
}

/**
 * Initialize location card animations with Intersection Observer
 */
function initLocationCardAnimations() {
    const cards = document.querySelectorAll('.location-card, .contact-card');

    if (cards.length && 'IntersectionObserver' in window) {
        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 150);

                    cardObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        cards.forEach(card => {
            // Set initial styles
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

            cardObserver.observe(card);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        cards.forEach(card => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        });
    }
}

/**
 * Initialize team member animations with Intersection Observer
 */
function initTeamAnimations() {
    const teamMembers = document.querySelectorAll('.team-member');

    if (teamMembers.length && 'IntersectionObserver' in window) {
        const teamObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 200);

                    teamObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        teamMembers.forEach(member => {
            // Set initial styles
            member.style.opacity = '0';
            member.style.transform = 'translateY(30px)';
            member.style.transition = 'opacity 0.8s ease, transform 0.8s ease';

            teamObserver.observe(member);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        teamMembers.forEach(member => {
            member.style.opacity = '1';
            member.style.transform = 'translateY(0)';
        });
    }
}

/**
 * Initialize stats counter with Intersection Observer
 */
function initStatsCounter() {
    const stats = document.querySelectorAll('.stat-number');
    if (!stats.length) return;

    // Group stats by their containers for better animation control
    const statGroups = {};

    stats.forEach(stat => {
        // Find the closest container
        const container = stat.closest('.intro-stats') ||
                         stat.closest('.company-achievements') ||
                         stat.closest('.trust-metrics') ||
                         stat.closest('.plant-stats') ||
                         stat.closest('.metric-item') ||
                         stat.closest('.achievement-item') ||
                         'default';

        // Create a group for this container if it doesn't exist
        if (!statGroups[container]) {
            statGroups[container] = [];
        }

        // Add the stat to its container group
        statGroups[container].push(stat);
    });

    function animateStats(statsToAnimate) {
        statsToAnimate.forEach(stat => {
            const target = parseInt(stat.getAttribute('data-count'));
            if (isNaN(target)) return;

            let current = 0;
            // Adjust speed based on target value
            const duration = 2000; // 2 seconds for all animations
            const steps = 60; // 60 steps (for 60fps)
            const increment = target / steps;
            const interval = duration / steps;

            const timer = setInterval(() => {
                current += increment;

                // Format large numbers with commas
                if (target > 999) {
                    stat.textContent = Math.floor(current).toLocaleString();
                } else {
                    stat.textContent = Math.floor(current);
                }

                if (current >= target) {
                    if (target > 999) {
                        stat.textContent = target.toLocaleString();
                    } else {
                        stat.textContent = target;
                    }
                    clearInterval(timer);
                }
            }, interval);
        });
    }

    // Use Intersection Observer to trigger animation when stats are in view
    if ('IntersectionObserver' in window) {
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Animate all stats in the container that was observed
                    if (entry.target.classList.contains('stat-number')) {
                        // If we're observing the stat directly
                        animateStats([entry.target]);
                    } else {
                        // If we're observing a container
                        const statsInContainer = entry.target.querySelectorAll('.stat-number');
                        if (statsInContainer.length > 0) {
                            animateStats(Array.from(statsInContainer));
                        }
                    }
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1, rootMargin: '0px 0px -100px 0px' });

        // Create a set of containers to observe
        const containersToObserve = new Set();

        // Find all containers with stats
        stats.forEach(stat => {
            const container = stat.closest('.intro-stats') ||
                             stat.closest('.company-achievements') ||
                             stat.closest('.trust-metrics') ||
                             stat.closest('.plant-stats') ||
                             stat.closest('.metric-item') ||
                             stat.closest('.achievement-item');

            if (container) {
                containersToObserve.add(container);
            } else {
                // If no container found, observe the stat itself
                statsObserver.observe(stat);
            }
        });

        // Observe each unique container
        containersToObserve.forEach(container => {
            statsObserver.observe(container);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        animateStats(Array.from(stats));
    }
}
